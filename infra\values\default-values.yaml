---

app:
  name: "${service_name}"
image:
  repository: "${container_registry}"
  tag: "${image_tag}"
replicas: "${replica}"
max_replicas: "${max_replica}"
resources:
  requests:
    cpu: "${cpu_request}"
    memory: "${mem_request}"
  limits:
    cpu: "${cpu_limit}"
    memory: "${mem_limit}"
service:
  name: "${service_name}"
  type: NodePort
  targetPort: "${service_port}"
  servicePort: "${service_port}"
  nodePort: "${node_port}"
  annotations:
    cloud.google.com/neg: '{"ingress": false}'   
ingress:
  enabled: "true"
  annotations:
    kubernetes.io/ingress.class: "gce"
    networking.gke.io/managed-certificates: "ssl-cert"
    kubernetes.io/ingress.allow-http: "false"
    kubernetes.io/ingress.global-static-ip-name: "${env}-fer2-ingress-ip"
  hosts:
     - host: ${dns}
       paths:
         - path: /
healthcheck:
  liveness:
    path: "${health_check_path}"
  readiness:  
    path: "${health_check_path}"         