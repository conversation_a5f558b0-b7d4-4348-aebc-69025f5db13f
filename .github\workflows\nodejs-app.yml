name: FrontendR2 Application CI/CD

on:
  push:
    branches:
      # - 'feature/**'
      - development
      - qa
      - pre_prod

jobs:
  # lint_and_test:
  #   runs-on: self-hosted
  #   container:
  #     image: hoosin/alpine-nginx-nodejs:latest
  #   steps:
  #     - name: Checkout code
  #       uses: actions/checkout@v3

  #     - name: Set up Node.js
  #       uses: actions/setup-node@v3
  #       with:
  #         node-version: 'latest'

  #     - name: Install dependencies
  #       run: npm install

  #     - name: Run ESLint
  #       run: |
  #         npx eslint --fix "src/**/*.{js,ts,tsx}"

  #     - name: Run Prettier
  #       run: |
  #         npx prettier --write "src/**/*.{js,ts,tsx,css,md,json}"

  #     - name: Run lint-staged
  #       run: npx lint-staged

  #test:
  #  runs-on: self-hosted
  #  if: startsWith(github.ref, 'refs/heads/development') || startsWith(github.ref, 'refs/heads/qa') || startsWith(github.ref, 'refs/heads/pre_prod')
  #  environment: ${{ github.ref_name }} 
  #  container: node:18-alpine
  #  steps:
  #    - name: Checkout code
  #      uses: actions/checkout@v3
#
  #    - name: Set up Node.js
  #      uses: actions/setup-node@v3
  #      with:
  #        node-version: '18'
#
  #    - name: Install dependencies
  #      run: npm install

      # - name: Run tests
      #   run: npm test

  build_and_push:
    runs-on: self-hosted 
    #needs: test 
    if: startsWith(github.ref, 'refs/heads/development') || startsWith(github.ref, 'refs/heads/qa') || startsWith(github.ref, 'refs/heads/pre_prod')
    environment: ${{ github.ref_name }} 
    steps:
      - name: Set Permission
        run: |
          sudo chown -R ubuntu:ubuntu .

      - name: Checkout code
        uses: actions/checkout@v3    

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1   

      - name: Build Docker image
        env:
          CONTAINER_REGISTRY: "europe-west2-docker.pkg.dev/${{ vars.PROJECT }}/${{ vars.DOCKER_REGISTRY }}/frontend"         
        run: |
          TAG_NAME=$(echo "${{ github.ref_name }}-${{ github.run_number }}" | sed 's/[^a-zA-Z0-9]/-/g')
          echo $TAG_NAME
          docker build --build-arg NODE_ENV=${{ vars.APP_ENV }} -t ${CONTAINER_REGISTRY}:${TAG_NAME} .
  
      - name: Push Docker image to GCR
        env:
          CONTAINER_REGISTRY: "europe-west2-docker.pkg.dev/${{ vars.PROJECT }}/${{ vars.DOCKER_REGISTRY }}/frontend"     
        run: |
          TAG_NAME=$(echo "${{ github.ref_name }}-${{ github.run_number }}" | sed 's/[^a-zA-Z0-9]/-/g')
          docker push ${CONTAINER_REGISTRY}:${TAG_NAME}    

  deploy:
    runs-on: self-hosted 
    needs: build_and_push 
    if: startsWith(github.ref, 'refs/heads/development') || startsWith(github.ref, 'refs/heads/qa') || startsWith(github.ref, 'refs/heads/pre_prod')
    environment: ${{ github.ref_name }} 
    container: hashicorp/terraform:1.11.4
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy
        env:
          GOOGLE_APPLICATION_CREDENTIALS: ${{ github.workspace }}/infra/terraform_service_account.json
          TERRAFORM_SERVICE_ACCOUNT_JSON: ${{ secrets.TERRAFORM_SERVICE_ACCOUNT_JSON }}
          CONTAINER_REGISTRY: "europe-west2-docker.pkg.dev/${{ vars.PROJECT }}/${{ vars.DOCKER_REGISTRY }}/frontend"   
        run: |
          TAG_NAME=$(echo "${{ github.ref_name }}-${{ github.run_number }}" | sed 's/[^a-zA-Z0-9]/-/g')
          cd infra
          printf '%s' "$TERRAFORM_SERVICE_ACCOUNT_JSON" > terraform_service_account.json
          terraform init
          terraform validate
          terraform workspace select "${{ vars.WORKSPACE }}" || terraform workspace new "${{ vars.WORKSPACE }}"
          terraform plan -var "project_id=${{ vars.PROJECT }}" -var "image_tag=${TAG_NAME}" -var "CONTAINER_REGISTRY=${CONTAINER_REGISTRY}" -out=tfplan -input=false
          terraform apply -input=false tfplan          
#
#  automation_tests:
#    runs-on: self-hosted
#    needs: deploy
#    if: startsWith(github.ref, 'refs/heads/development') || startsWith(github.ref, 'refs/heads/qa') || startsWith(github.ref, 'refs/heads/pre_prod')
#    defaults:
#      run:
#        working-directory: ./test
#
#    steps:
#      - name: Clean up before checkout
#        run: sudo chown -R ubuntu:ubuntu ${{ github.workspace }} && sudo chown -R ubuntu:ubuntu /home/<USER>/actions-runner/_work/_tool/node
#
#      - name: Checkout code
#        uses: actions/checkout@v3
#
#      - name: Set up Node.js
#        uses: actions/setup-node@v3
#        with:
#          node-version: '18'
#
#      - name: Install dependencies
#        run: npm install && npx playwright install-deps && npx playwright install     
#
#      - name: Project setup
#        run: npm run setup
#
#      - name: Run flight booking tests
#        run: |
#          if [[ "${GITHUB_REF##*/}" == "qa" ]]; then
#            npm run test:flightbooking:qa
#          else
#            npm run test:flightbooking
#          fi
#
#      - name: Generate report
#        if: always()
#        run: npm run generate-report
#
#      - name: Upload Cucumber HTML Report
#        if: always()
#        uses: actions/upload-artifact@v4
#        with:
#          name: cucumber-html-report
#          path: ./test/test-reports/cucumber-html-reports/          
#